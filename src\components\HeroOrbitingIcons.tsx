import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Code,
  Camera,
  Palette,
  Monitor,
  Smartphone,
  Aperture,
  Lightbulb,
  Star,
  Diamond,
  Wifi,
  Cloud,
  MousePointer,
  Zap,
  Sparkles,
  Brush,
  Cpu,
  Database,
  Globe,
  Headphones,
  Image,
  Layers,
  Mic,
  Music,
  Play,
  Rocket,
  Search,
  Settings,
  Shield,
  Target,
  Tv,
  Users,
  Video,
  Wand2,
  Wrench,
  Eye,
  Heart
} from 'lucide-react';

// Hero-specific icons for orbital animation
// Optimized version based on working PricingOrbitingIcons with creative/tech themes

// ENHANCED 4-Path Orbital System - Hero-focused with maximum visual dominance
const heroOrbitingIcons = [
  // Inner Path - Clockwise rotation (35s) - Core Creative Elements (6 icons, 60° spacing) - ENHANCED
  { Icon: Diamond, color: 'text-premium-platinum/70', size: 50, radius: 240, sector: 0, orbitalFamily: 'inner' },
  { Icon: Lightbulb, color: 'text-premium-gold/65', size: 52, radius: 240, sector: 60, orbitalFamily: 'inner' },
  { Icon: Sparkles, color: 'text-neon-cyan/65', size: 50, radius: 240, sector: 120, orbitalFamily: 'inner' },
  { Icon: Star, color: 'text-neon-purple/65', size: 51, radius: 240, sector: 180, orbitalFamily: 'inner' },
  { Icon: Heart, color: 'text-neon-pink/65', size: 49, radius: 240, sector: 240, orbitalFamily: 'inner' },
  { Icon: Wand2, color: 'text-premium-gold/60', size: 50, radius: 240, sector: 300, orbitalFamily: 'inner' },

  // Middle Path - Counterclockwise rotation (50s) - Technology & Design Focus (8 icons, 45° spacing) - ENHANCED
  { Icon: Camera, color: 'text-neon-purple/60', size: 45, radius: 340, sector: 0, orbitalFamily: 'middle' },
  { Icon: Code, color: 'text-neon-cyan/60', size: 47, radius: 340, sector: 45, orbitalFamily: 'middle' },
  { Icon: Palette, color: 'text-neon-blue/60', size: 45, radius: 340, sector: 90, orbitalFamily: 'middle' },
  { Icon: Aperture, color: 'text-premium-gold/60', size: 44, radius: 340, sector: 135, orbitalFamily: 'middle' },
  { Icon: Zap, color: 'text-neon-green/60', size: 47, radius: 340, sector: 180, orbitalFamily: 'middle' },
  { Icon: Brush, color: 'text-neon-orange/60', size: 42, radius: 340, sector: 225, orbitalFamily: 'middle' },
  { Icon: Layers, color: 'text-premium-sapphire/60', size: 45, radius: 340, sector: 270, orbitalFamily: 'middle' },
  { Icon: Image, color: 'text-neon-purple/55', size: 44, radius: 340, sector: 315, orbitalFamily: 'middle' },

  // Outer Path - Clockwise rotation (70s) - Professional Tools (10 icons, 36° spacing) - ENHANCED
  { Icon: Monitor, color: 'text-premium-sapphire/55', size: 40, radius: 440, sector: 0, orbitalFamily: 'outer' },
  { Icon: Smartphone, color: 'text-neon-green/55', size: 38, radius: 440, sector: 36, orbitalFamily: 'outer' },
  { Icon: Cloud, color: 'text-premium-silver/55', size: 40, radius: 440, sector: 72, orbitalFamily: 'outer' },
  { Icon: Wifi, color: 'text-neon-cyan/55', size: 39, radius: 440, sector: 108, orbitalFamily: 'outer' },
  { Icon: MousePointer, color: 'text-neon-blue/55', size: 38, radius: 440, sector: 144, orbitalFamily: 'outer' },
  { Icon: Database, color: 'text-premium-platinum/55', size: 40, radius: 440, sector: 180, orbitalFamily: 'outer' },
  { Icon: Globe, color: 'text-neon-green/50', size: 39, radius: 440, sector: 216, orbitalFamily: 'outer' },
  { Icon: Settings, color: 'text-premium-silver/50', size: 38, radius: 440, sector: 252, orbitalFamily: 'outer' },
  { Icon: Rocket, color: 'text-neon-orange/55', size: 40, radius: 440, sector: 288, orbitalFamily: 'outer' },
  { Icon: Shield, color: 'text-premium-sapphire/50', size: 39, radius: 440, sector: 324, orbitalFamily: 'outer' },

  // Ultra Path - Counterclockwise rotation (100s) - Ambient Creative Elements (12 icons, 30° spacing) - ENHANCED
  { Icon: Cpu, color: 'text-premium-platinum/45', size: 35, radius: 540, sector: 0, orbitalFamily: 'ultra' },
  { Icon: Headphones, color: 'text-neon-cyan/45', size: 34, radius: 540, sector: 30, orbitalFamily: 'ultra' },
  { Icon: Mic, color: 'text-premium-gold/45', size: 33, radius: 540, sector: 60, orbitalFamily: 'ultra' },
  { Icon: Music, color: 'text-neon-purple/45', size: 35, radius: 540, sector: 90, orbitalFamily: 'ultra' },
  { Icon: Play, color: 'text-neon-green/45', size: 34, radius: 540, sector: 120, orbitalFamily: 'ultra' },
  { Icon: Search, color: 'text-premium-silver/45', size: 33, radius: 540, sector: 150, orbitalFamily: 'ultra' },
  { Icon: Target, color: 'text-neon-blue/45', size: 35, radius: 540, sector: 180, orbitalFamily: 'ultra' },
  { Icon: Tv, color: 'text-premium-sapphire/45', size: 34, radius: 540, sector: 210, orbitalFamily: 'ultra' },
  { Icon: Users, color: 'text-neon-orange/45', size: 33, radius: 540, sector: 240, orbitalFamily: 'ultra' },
  { Icon: Video, color: 'text-premium-gold/40', size: 35, radius: 540, sector: 270, orbitalFamily: 'ultra' },
  { Icon: Wrench, color: 'text-premium-silver/40', size: 34, radius: 540, sector: 300, orbitalFamily: 'ultra' },
  { Icon: Eye, color: 'text-neon-cyan/40', size: 33, radius: 540, sector: 330, orbitalFamily: 'ultra' }
];

// ENHANCED 4-Path Orbital Configuration - Optimized for hero section
const heroOrbitalConfig = {
  inner: {
    baseSpeed: 35,      // Energetic, core rotation
    direction: 1,       // Clockwise (primary direction)
    phaseOffset: 0,     // Primary reference point
    iconCount: 6,       // 6 icons, 60° spacing
    radius: 240         // ENHANCED: Optimized radius for hero section
  },
  middle: {
    baseSpeed: 50,      // Balanced medium speed
    direction: -1,      // Counterclockwise (creates visual contrast)
    phaseOffset: 30,    // 30° offset for harmonic spacing
    iconCount: 8,       // 8 icons, 45° spacing
    radius: 340         // ENHANCED: Optimized radius for hero section
  },
  outer: {
    baseSpeed: 70,      // Professional, steady rotation
    direction: 1,       // Clockwise (harmonizes with inner)
    phaseOffset: 18,    // 18° offset for coordinated positioning
    iconCount: 10,      // 10 icons, 36° spacing
    radius: 440         // ENHANCED: Optimized radius for hero section
  },
  ultra: {
    baseSpeed: 100,     // Slow, stately background rotation
    direction: -1,      // Counterclockwise (alternating pattern)
    phaseOffset: 15,    // 15° offset for subtle depth
    iconCount: 12,      // 12 icons, 30° spacing
    radius: 540         // ENHANCED: Optimized radius for hero section
  }
};

// Interface for orbital configuration
interface OrbitalConfig {
  orbitalFamily: string;
  sector: number;
  Icon: React.ElementType;
  color: string;
  size: number;
  radius: number;
}

// Calculate organized orbital state with staggered initial positions
const calculateHeroOrganizedState = (config: OrbitalConfig, _index: number) => {
  const { orbitalFamily, sector } = config;
  const familyConfig = heroOrbitalConfig[orbitalFamily as keyof typeof heroOrbitalConfig];
  
  // Calculate staggered initial rotation for continuous-running appearance
  const baseInitialRotation = (sector + familyConfig.phaseOffset) % 360;
  
  // Add time-based offset to simulate mid-cycle positioning
  const timeOffset = (Date.now() / 100) % 360;
  const initialRotation = (baseInitialRotation + timeOffset) % 360;
  
  return {
    initialRotation,
    actualSpeed: familyConfig.baseSpeed,
    direction: familyConfig.direction,
    pathRadius: familyConfig.radius
  };
};

// Smooth, coordinated orbital animation variants for hero section
const createHeroOrbitVariants = (speed: number, initialRotation: number, direction: number) => ({
  initial: {
    rotate: initialRotation,
  },
  animate: {
    rotate: initialRotation + (360 * direction),
    transition: {
      duration: speed,
      repeat: Infinity,
      ease: "linear",
      repeatType: "loop" as const
    }
  }
});

// Enhanced floating animation variants for hero icons
const createHeroFloatingVariants = (orbitalFamily: string, index: number) => {
  // Coordinated floating timing optimized for each path
  const floatingConfigs = {
    inner: { duration: 5.2, amplitude: 2.0, scaleRange: 1.04 },   // Energetic floating for inner path
    middle: { duration: 6.8, amplitude: 2.4, scaleRange: 1.03 },  // Dynamic floating for middle path
    outer: { duration: 8.5, amplitude: 1.8, scaleRange: 1.025 },  // Balanced floating for outer path
    ultra: { duration: 11.2, amplitude: 1.4, scaleRange: 1.015 }  // Subtle floating for ultra path
  };

  const floatingConfig = floatingConfigs[orbitalFamily as keyof typeof floatingConfigs] ||
    { duration: 7, amplitude: 2, scaleRange: 1.025 };

  // Create harmonic delay pattern based on path position
  const harmonicDelayMap = {
    inner: index * 0.35,   // Quick succession for inner path
    middle: index * 0.45,  // Medium-quick staggering for middle path
    outer: index * 0.6,    // Balanced staggering for outer path
    ultra: index * 0.8     // Gentle staggering for ultra path
  };
  const harmonicDelay = harmonicDelayMap[orbitalFamily as keyof typeof harmonicDelayMap] || index * 0.5;

  // ENHANCED: Path-specific opacity ranges for maximum visibility and dynamic depth perception
  const opacityRangeMap = {
    inner: [0.95, 1.0, 0.95],      // ENHANCED: Maximum visibility for inner path
    middle: [0.9, 0.98, 0.9],      // ENHANCED: High visibility for middle path
    outer: [0.85, 0.95, 0.85],     // ENHANCED: Strong visibility for outer path
    ultra: [0.8, 0.9, 0.8]         // ENHANCED: Good visibility for ultra path
  };
  const opacityRange = opacityRangeMap[orbitalFamily as keyof typeof opacityRangeMap] || [0.85, 0.95, 0.85];

  return {
    initial: {
      y: 0,
      scale: 1,
      opacity: opacityRange[0]
    },
    animate: {
      y: [-floatingConfig.amplitude, floatingConfig.amplitude, -floatingConfig.amplitude],
      scale: [1, floatingConfig.scaleRange, 1],
      opacity: opacityRange,
      transition: {
        duration: floatingConfig.duration,
        repeat: Infinity,
        ease: "easeInOut",
        delay: harmonicDelay
      }
    }
  };
};

interface HeroOrbitingIconsProps {
  className?: string;
}

const HeroOrbitingIcons: React.FC<HeroOrbitingIconsProps> = ({ className = '' }) => {
  // Add responsive width detection with SSR safety
  const [isSmallMobile, setIsSmallMobile] = useState(false);
  
  useEffect(() => {
    // Only run in the browser, not during SSR
    const handleResize = () => {
      setIsSmallMobile(window.innerWidth < 375);
    };
    
    // Initial check
    handleResize();
    
    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div
      className={`absolute inset-0 pointer-events-none overflow-hidden hero-orbital-container ${className}`}
      aria-hidden="true"
      role="presentation"
    >
      {/* Enhanced responsive visibility with accessibility support */}
      <div className="hidden md:block motion-safe:block motion-reduce:hidden">
        {/* Enhanced 4-Path Hero Orbital System with coordinated animations */}
        {heroOrbitingIcons.map((config, index) => {
          const { Icon, color, size, orbitalFamily, sector } = config;

          // Calculate enhanced orbital mechanics for this hero icon
          const { initialRotation, actualSpeed, direction, pathRadius } =
            calculateHeroOrganizedState(config, index);

          // Enhanced z-index layering for 4-path system
          const zIndexMap = {
            inner: 4,
            middle: 3,
            outer: 2,
            ultra: 1
          };

          // ENHANCED: Path-specific opacity for maximum visibility while maintaining depth perception
          const baseOpacity = {
            inner: 1.0,     // ENHANCED: Maximum visibility for inner path
            middle: 0.95,   // ENHANCED: High visibility for middle path
            outer: 0.9,     // ENHANCED: Strong visibility for outer path
            ultra: 0.85     // ENHANCED: Good visibility for ultra path
          }[orbitalFamily] || 0.9;

          return (
            <motion.div
              key={`hero-orbital-${orbitalFamily}-${sector}-${index}`}
              className={`orbital-icon absolute top-1/2 left-1/2 ${color}`}
              style={{
                width: pathRadius * 2,
                height: pathRadius * 2,
                marginLeft: -pathRadius,
                marginTop: -pathRadius,
                willChange: 'transform',
                transform: 'translate3d(0, 0, 0)', // Hardware acceleration
                zIndex: zIndexMap[orbitalFamily as keyof typeof zIndexMap]
              }}
              variants={createHeroOrbitVariants(actualSpeed, initialRotation, direction)}
              initial="initial"
              animate="animate"
            >
              <motion.div
                className="absolute"
                style={{
                  top: 0, // Positioned on outer edge of orbital path
                  left: '50%',
                  transform: 'translateX(-50%)',
                  willChange: 'transform'
                }}
                variants={createHeroFloatingVariants(orbitalFamily, index)}
                initial="initial"
                animate="animate"
              >
                <Icon
                  size={size}
                  strokeWidth={2.5}  // ENHANCED: Increased stroke width for visual weight
                  className="drop-shadow-lg transition-opacity duration-300 filter blur-0 hover:opacity-100"
                  style={{
                    willChange: 'opacity',
                    opacity: baseOpacity
                  }}
                />
              </motion.div>
            </motion.div>
          );
        })}
      </div>

      {/* Mobile-optimized version with reduced complexity for better performance */}
      <div className="block md:hidden motion-safe:block motion-reduce:hidden">
        {/* Only show inner and middle paths on small mobile for better performance and reduced visual complexity */}
        {heroOrbitingIcons.filter(config =>
          (isSmallMobile ? config.orbitalFamily === 'inner' : config.orbitalFamily === 'inner' || config.orbitalFamily === 'middle')
        ).map((config, index) => {
          const { Icon, color, size, radius, orbitalFamily, sector } = config;
          const { initialRotation, actualSpeed, direction } = calculateHeroOrganizedState(config, index);

          // Mobile-specific sizing with improved proportional scaling for better small screen display
          const mobileSize = Math.max(size * 0.65, 18); // Smaller icons for mobile
          const mobileRadius = radius * 0.5;           // Smaller orbit radius for mobile

          return (
            <motion.div
              key={`hero-mobile-orbital-${orbitalFamily}-${sector}-${index}`}
              className={`orbital-icon absolute top-1/2 left-1/2 ${color}`}
              style={{
                width: mobileRadius * 2,
                height: mobileRadius * 2,
                marginTop: -mobileRadius,
                marginLeft: -mobileRadius,
                willChange: 'transform',
                transform: 'translate3d(0, 0, 0)' // Hardware acceleration
              }}
              variants={createHeroOrbitVariants(actualSpeed * 1.3, initialRotation, direction)} // Faster on mobile for better perceived performance
              initial="initial"
              animate="animate"
            >
              <motion.div
                className="absolute"
                style={{
                  top: 0,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  willChange: 'transform'
                }}
                variants={createHeroFloatingVariants(orbitalFamily, index)}
                initial="initial"
                animate="animate"
              >
                <Icon
                  size={mobileSize}
                  strokeWidth={2.5}  // ENHANCED: Increased stroke width for mobile visual weight
                  className="drop-shadow-lg transition-opacity duration-300"
                  style={{
                    willChange: 'opacity',
                    opacity: 0.8  // ENHANCED: High mobile visibility
                  }}
                />
              </motion.div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default HeroOrbitingIcons; 