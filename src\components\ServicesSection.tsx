import React, { useRef } from 'react';
import { useLanguage } from '@/hooks/use-language';
import WebDesignCard from './WebDesignCard';
import PhotoCard from './PhotoCard';
import { motion } from 'framer-motion';

const ServicesSection: React.FC = () => {
  const { t } = useLanguage();
  const sectionRef = useRef<HTMLElement>(null);

  return (
    <section
      id="services"
      className="relative py-24 md:py-36 px-4 md:px-8 overflow-hidden"
      ref={sectionRef}
    >
      {/* Unified background layers for optimal readability and visual harmony */}
      <div className="absolute inset-0 bg-gradient-cosmic-dark"></div>
      <div className="absolute inset-0 bg-gradient-cosmic opacity-50"></div>
      <div className="absolute inset-0 bg-gradient-radial from-neon-purple/4 via-transparent to-neon-cyan/2"></div>
      <div className="absolute inset-0 bg-gradient-aurora opacity-1"></div>
      <div className="absolute inset-0 bg-black/40"></div>



      {/* Animated grid lines */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 grid grid-cols-6 pointer-events-none">
          {[...Array(7)].map((_, i) => (
            <motion.div 
              key={i} 
              className="border-l border-white/10 h-full"
              initial={{ height: "0%" }}
              animate={{ height: "100%" }}
              transition={{ duration: 1.5, delay: i * 0.1 }}
            />
          ))}
        </div>
        <div className="absolute inset-0 grid grid-rows-6 pointer-events-none">
          {[...Array(7)].map((_, i) => (
            <motion.div 
              key={i} 
              className="border-t border-white/10 w-full"
              initial={{ width: "0%" }}
              animate={{ width: "100%" }}
              transition={{ duration: 1.5, delay: i * 0.1 }}
            />
          ))}
        </div>
      </div>

      <div className="section-container">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, margin: "-100px" }}
          className="section-header"
        >
          <h2 className="section-title-enhanced">
            <span className="text-gradient-lightning-white">{t('services.title')}</span>
          </h2>
          <motion.p
            className="section-description"
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
          >
            We transform ideas into stunning digital experiences through cutting-edge web design
            and captivating product photography.
          </motion.p>
        </motion.div>
        
        {/* Add visual connector between cards */}
        <div className="relative">
          <motion.div
            className="absolute inset-y-0 left-1/2 w-px bg-gradient-to-b from-transparent via-neon-purple to-transparent transform -translate-x-1/2 md:block hidden"
            initial={{ scaleY: 0 }}
            whileInView={{ scaleY: 1 }}
            transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
            viewport={{ once: true, margin: "0px 0px -100px 0px" }}
          ></motion.div>
          <div className="card-grid-2 relative z-10">
            <div className="h-full">
              <WebDesignCard />
            </div>
            
            <div className="h-full">
              <PhotoCard />
            </div>
          </div>
        </div>
        
        {/* Decorative elements */}
        <div className="absolute -bottom-8 left-1/4 w-32 h-32 bg-gradient-to-r from-neon-cyan/10 to-neon-purple/10 rounded-full blur-3xl pointer-events-none"></div>
        <div className="absolute -top-16 right-1/3 w-40 h-40 bg-gradient-to-r from-neon-purple/10 to-neon-cyan/10 rounded-full blur-3xl pointer-events-none"></div>
      </div>
    </section>
  );
};

export default ServicesSection;
