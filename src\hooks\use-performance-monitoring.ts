import { useEffect } from 'react';

/**
 * Hook for monitoring and optimizing performance metrics
 * Helps improve Core Web Vitals and Vercel Speed Insights scores
 * Enhanced with better error handling and browser extension filtering
 */
export function usePerformanceMonitoring() {
  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') return;

    // Check if PerformanceObserver is available
    if (!window.PerformanceObserver) {
      // Silent fallback - no console output needed in development
      return;
    }

    // Only run performance monitoring in production or when explicitly enabled
    if (import.meta.env.MODE !== 'production' && !import.meta.env.VITE_ENABLE_PERF_MONITORING) {
      return;
    }

    // Enhanced error handler for performance monitoring
    const handlePerformanceError = (error: Error | unknown, context: string) => {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Filter out browser extension related errors
      if (errorMessage.includes('Frame with ID') ||
          errorMessage.includes('No tab with id') ||
          errorMessage.includes('Extension context') ||
          errorMessage.includes('background.js') ||
          errorMessage.includes('chrome-extension://') ||
          errorMessage.includes('moz-extension://')) {
        return; // Silently ignore extension-related errors
      }

      // Only log actual performance monitoring errors in production
      if (import.meta.env.MODE === 'production') {
        console.error(`Performance monitoring ${context} error:`, error);
      }
    };

    // Report Web Vitals metrics for Speed Insights
    try {
      // Monitor First Input Delay (FID) - only if supported
      try {
        new PerformanceObserver((entries) => {
          try {
            entries.getEntries().forEach((_entry) => {
              // Only report FID in production for actual monitoring
              if (import.meta.env.MODE === 'production') {
                // Send to analytics service in production
                // console.log('FID:', entry);
              }
            });
          } catch (_error) {
            handlePerformanceError(_error, 'FID processing');
          }
        }).observe({ type: 'first-input', buffered: true });
      } catch (_error) {
        // FID might not be supported in all browsers
        if (import.meta.env.MODE === 'development') {
          console.debug('FID monitoring not supported');
        }
      }

      // Monitor Largest Contentful Paint (LCP) - only if supported
      try {
        new PerformanceObserver((entries) => {
          try {
            entries.getEntries().forEach((_entry) => {
              // Only report LCP in production for actual monitoring
              if (import.meta.env.MODE === 'production') {
                // Send to analytics service in production
                // console.log('LCP:', entry);
              }
            });
          } catch (_error) {
            handlePerformanceError(_error, 'LCP processing');
          }
        }).observe({ type: 'largest-contentful-paint', buffered: true });
      } catch (_error) {
        // LCP might not be supported in all browsers - silent fallback
      }

      // Monitor Cumulative Layout Shift (CLS) - only if supported
      try {
        new PerformanceObserver((entries) => {
          try {
            entries.getEntries().forEach((_entry) => {
              // Only report CLS in production for actual monitoring
              if (import.meta.env.MODE === 'production') {
                // Send to analytics service in production
                // console.log('CLS:', entry);
              }
            });
          } catch (_error) {
            handlePerformanceError(_error, 'CLS processing');
          }
        }).observe({ type: 'layout-shift', buffered: true });
      } catch (_error) {
        // CLS might not be supported in all browsers - silent fallback
      }
    } catch (error) {
      handlePerformanceError(error, 'setup');
    }
  }, []);
}

export default usePerformanceMonitoring;
