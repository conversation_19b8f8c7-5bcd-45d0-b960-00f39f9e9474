/**
 * Service Worker Registration for improved caching and performance
 * Enhanced with better error handling and frame management
 */

export function registerServiceWorker() {
  // Skip service worker in development for cleaner console output
  if (import.meta.env.DEV && !import.meta.env.VITE_ENABLE_SW) {
    return;
  }

  if ('serviceWorker' in navigator) {
    // Handle service worker errors with filtering
    const handleServiceWorkerError = (error: unknown) => {
      let errorMessage = '';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else {
        errorMessage = String(error);
      }
      // Enhanced filtering for development noise
      const developmentPatterns = [
        'Frame with ID', 'No tab with id', 'message port closed', 'Extension context',
        'Development mode', 'localhost', 'minimal functionality', 'registration skipped'
      ];

      // Filter out extension-related and development errors
      if (developmentPatterns.some(pattern => errorMessage.includes(pattern))) {
        return;
      }

      // Only log in production
      if (import.meta.env.PROD) {
        console.error('Service Worker error:', error);
      }
    };

    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/service-worker.js', {
        // Add scope and update options for better frame management
        scope: '/',
        updateViaCache: 'none'
      })
        .then(registration => {
          // Silent registration in development, log only in production
          if (import.meta.env.PROD) {
            console.log('Service Worker registered with scope:', registration.scope);
          }

          // Handle service worker updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller && import.meta.env.PROD) {
                  console.log('New service worker available');
                }
              });
              newWorker.addEventListener('error', handleServiceWorkerError);
            }
          });

          registration.addEventListener('error', handleServiceWorkerError);
        })
        .catch(error => {
          handleServiceWorkerError(error);
        });

      // Add global service worker error handling
      navigator.serviceWorker.addEventListener('error', handleServiceWorkerError);

      // Handle service worker controller changes
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        if (import.meta.env.PROD) {
          console.log('Service worker controller changed');
        }
      });
    });
  }
}

export function unregisterServiceWorker() {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations()
      .then(registrations => {
        const unregisterPromises = registrations.map(registration => {
          return registration.unregister().catch(error => {
            if (import.meta.env.PROD) {
              console.warn('Failed to unregister service worker:', error);
            }
          });
        });
        return Promise.all(unregisterPromises);
      })
      .catch(error => {
        if (import.meta.env.PROD) {
          console.warn('Failed to get service worker registrations:', error);
        }
      });
  }
}

/**
 * Clean up service worker related errors and event listeners
 * Call this function when the app is unmounting or during cleanup
 */
export function cleanupServiceWorker() {
  if ('serviceWorker' in navigator) {
    try {
      // Remove event listeners to prevent memory leaks
      navigator.serviceWorker.removeEventListener('error', () => {});
      navigator.serviceWorker.removeEventListener('controllerchange', () => {});
    } catch (_error) {
      // Silent cleanup - no console output needed
    }
  }
}
